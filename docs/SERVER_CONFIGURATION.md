# Server Configuration Guide

This document provides details on configuring and running the Rylie AI server.

## Server Port Configuration

By default, the Rylie AI server runs on **port 5000**. This can be configured through the `PORT` environment variable:

```
PORT=5000  # Default server port
```

When testing the platform, ensure you're connecting to the correct port. Many test scripts and tools may default to port 3000, which will not work unless you've explicitly changed the server configuration.

## Starting the Server

### Development Mode

To start the server in development mode:

```bash
npm run dev
```

This will:
- Use the development environment settings
- Enable hot reloading for code changes
- Serve the frontend from the Vite development server

### Production Mode

To start the server in production mode:

```bash
npm run start
```

This will:
- Use the production environment settings
- Serve optimized frontend assets
- Disable development-only features

## Server Health Check

The server provides a health check endpoint at:

```
GET /api/health
```

This endpoint returns:
- HTTP 200 OK when the server is running properly
- Database connection status
- Redis connection status (if configured)
- API integration status

Use this endpoint for monitoring and to verify the server is operational.

## Troubleshooting Server Issues

If the server fails to start or respond:

1. Check the server logs for error messages
2. Verify the correct port is being used (5000 by default)
3. Ensure all required environment variables are set
4. Check database connectivity
5. Verify Redis connectivity (if not using fallback mode)

Common error messages and solutions:

| Error | Solution |
|-------|----------|
| `EADDRINUSE` | Port 5000 is already in use. Either stop the other process or change the PORT environment variable |
| `Database connection failed` | Check DATABASE_URL and ensure PostgreSQL is running |
| `Redis connection failed` | Check Redis configuration or set SKIP_REDIS=true to use fallback |
| `Missing environment variable` | Ensure all required variables are set in .env file |