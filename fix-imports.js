#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files that need logger import fixes
const filesToFix = [
  // Files importing from './logger' (in utils directory)
  'server/utils/error-handler.ts',
  'server/utils/redis-config.ts',
  'server/utils/migration-runner.ts',
  'server/utils/error-codes.ts',

  // Files importing from '../logger' (in other directories)
  'server/middleware/tenant-context.ts',
  'server/standalone-routes/public-magic-link.ts',
  'server/controllers/authController.ts',
  'server/routes/magic-link.ts',
  'server/routes/admin-user-routes.ts',
  'server/routes/local-auth-routes.ts',
  'server/routes/admin-routes.ts',
  'server/services/magic-link-auth.ts'
];

function fixImports() {
  console.log('🔧 Fixing logger import issues...\n');

  filesToFix.forEach(filePath => {
    const fullPath = path.resolve(__dirname, filePath);

    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      const originalContent = content;

      // Fix imports from './logger' to './logger' (for utils files)
      if (filePath.includes('server/utils/')) {
        // These files are in utils directory, so they should import from same directory
        // But the logger is actually in utils/logger.ts, so no change needed
        console.log(`ℹ️  Utils file, checking: ${filePath}`);
      } else {
        // Fix imports from '../logger' to '../utils/logger'
        content = content.replace(
          /import\s*{\s*logger\s*}\s*from\s*['"`]\.\.\/logger['"`];?/g,
          "import logger from '../utils/logger';"
        );
        content = content.replace(
          /import\s+logger\s+from\s*['"`]\.\.\/logger['"`];?/g,
          "import logger from '../utils/logger';"
        );
      }

      // Write back if changed
      if (content !== originalContent) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed logger imports in: ${filePath}`);
      } else {
        console.log(`ℹ️  No changes needed in: ${filePath}`);
      }

    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
  });

  console.log('\n🎉 Logger import fix process completed!');
}

// Run the fix
fixImports();