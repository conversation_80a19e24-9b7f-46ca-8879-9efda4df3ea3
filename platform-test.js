#!/usr/bin/env node

/**
 * Simple platform test script to verify basic functionality
 */

import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 5001;

app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    platform: 'Rylie AI',
    version: '1.0.0'
  });
});

// Test conversation endpoint
app.post('/api/test-conversation', (req, res) => {
  const { message, customerName } = req.body;

  const responses = [
    "Hello! I'm <PERSON><PERSON><PERSON>, your automotive AI assistant. How can I help you find the perfect vehicle today?",
    "I'd be happy to help you with that! Let me check our inventory for vehicles that match your needs.",
    "That's a great choice! Would you like to schedule a test drive or learn more about financing options?"
  ];

  const randomResponse = responses[Math.floor(Math.random() * responses.length)];

  res.json({
    success: true,
    conversation: {
      id: `conv_${Date.now()}`,
      customer: customerName || 'Customer',
      messages: [
        {
          id: `msg_${Date.now()}_1`,
          sender: 'customer',
          content: message,
          timestamp: new Date().toISOString()
        },
        {
          id: `msg_${Date.now()}_2`,
          sender: 'ai',
          content: randomResponse,
          timestamp: new Date().toISOString()
        }
      ]
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    message: `Endpoint ${req.method} ${req.originalUrl} not found`
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Rylie AI Test Server running on http://localhost:${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   POST http://localhost:${PORT}/api/test-conversation`);
  console.log(`\n✅ Platform test server is ready!`);
});
