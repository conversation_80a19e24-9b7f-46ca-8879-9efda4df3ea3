import { Router, Request, Response } from 'express';
import logger from '../utils/logger';
import { loginUser, registerUser, logoutUser, getCurrentUser } from '../controllers/authController';

const router = Router();

// Login endpoint - using bypass authentication controller
router.post('/login', async (req: Request, res: Response) => {
  try {
    // Call the auth controller which bypasses actual authentication
    await loginUser(req, res);
    
    logger.info(`User logged in using bypass authentication`);
  } catch (error) {
    logger.error('Login error:', error);
    return res.status(500).json({ error: 'An error occurred during login' });
  }
});

// Register endpoint - using bypass authentication controller
router.post('/register', async (req: Request, res: Response) => {
  try {
    // Call the auth controller which bypasses actual registration
    await registerUser(req, res);
    
    logger.info(`User registered using bypass authentication`);
  } catch (error) {
    logger.error('Registration error:', error);
    return res.status(500).json({ error: 'An error occurred during registration' });
  }
});

// Get current user endpoint - using bypass authentication controller
router.get('/user', async (req: Request, res: Response) => {
  try {
    // Call the auth controller which returns a mock user
    await getCurrentUser(req, res);
  } catch (error) {
    logger.error('Get user error:', error);
    return res.status(500).json({ error: 'An error occurred while fetching user data' });
  }
});

// Logout endpoint - using bypass authentication controller
router.post('/logout', async (req: Request, res: Response) => {
  try {
    // Call the auth controller which bypasses actual logout
    await logoutUser(req, res);
    
    // Also clean up session if it exists
    if (req.session) {
      req.session.destroy((err) => {
        if (err) {
          logger.error('Session destruction error:', err);
        }
        res.clearCookie('connect.sid');
      });
    }
  } catch (error) {
    logger.error('Logout error:', error);
    return res.status(500).json({ error: 'An error occurred during logout' });
  }
});

export default router;