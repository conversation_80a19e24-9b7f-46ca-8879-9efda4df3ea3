/**
 * Service for user management and invitations
 */
import db from "../db";
import { users, dealerships, UserRole } from '../../shared/enhanced-schema';
import { userInvitations, auditLogs } from '../../shared/schema-extensions';
import { eq, and, gte } from 'drizzle-orm';
import { sendEmail } from './email-service';
import { randomBytes } from 'crypto';
import bcrypt from 'bcrypt';
import logger from '../utils/logger';

/**
 * Create a user invitation
 */
export async function createUserInvitation({
  email,
  role,
  dealershipId,
  invitedBy
}: {
  email: string;
  role: string;
  dealershipId: number;
  invitedBy: number;
}) {
  // Generate a secure token
  const token = randomBytes(32).toString('hex');

  // Set expiration to 7 days from now
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7);

  // Create invitation record
  const [invitation] = await db.insert(userInvitations).values({
    email,
    role,
    dealershipId,
    invitedBy,
    token,
    expiresAt,
    status: 'pending'
  }).returning();

  // Log the action
  await logAuditEvent({
    userId: invitedBy,
    dealershipId,
    action: 'create_invitation',
    resourceType: 'user_invitation',
    resourceId: invitation.id,
    details: { email, role }
  });

  // Send invitation email
  const inviteUrl = `${process.env.APP_URL}/accept-invitation?token=${token}`;

  // Get inviter's name
  const [inviter] = await db.select().from(users).where(eq(users.id, invitedBy));
  const inviterName = inviter?.name || 'A dealership administrator';

  // Get dealership name
  const [dealership] = await db.select().from(dealerships).where(eq(dealerships.id, dealershipId));
  const dealershipName = dealership?.name || 'our dealership';

  const emailText = `
      ${inviterName} has invited you to join ${dealershipName} on Rylie AI.

      Click the link below to accept the invitation and create your account:
      ${inviteUrl}

      This invitation will expire in 7 days.
    `;

  const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0;">
        <h2 style="color: #1a73e8;">You've been invited to join Rylie AI</h2>
        <p>${inviterName} has invited you to join <strong>${dealershipName}</strong> on Rylie AI.</p>
        <p>Click the button below to accept the invitation and create your account:</p>
        <p style="margin: 30px 0;">
          <a href="${inviteUrl}" style="background-color: #1a73e8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Accept Invitation</a>
        </p>
        <p style="color: #666; font-size: 14px;">This invitation will expire in 7 days.</p>
      </div>
    `;

  await sendEmail(
    email,
    `Invitation to join ${dealershipName} on Rylie AI`,
    emailText,
    emailHtml
  );

  return invitation;
}

/**
 * Verify and accept a user invitation
 */
export async function acceptUserInvitation(token: string, userData: {
  name: string;
  password: string;
}) {
  // Find the invitation
  const [invitation] = await db.select().from(userInvitations)
    .where(and(
      eq(userInvitations.token, token),
      eq(userInvitations.status, 'pending'),
      gte(userInvitations.expiresAt, new Date())
    ));

  if (!invitation) {
    throw new Error('Invalid or expired invitation');
  }

  // Create the user - match exact schema field names
  const [user] = await db.insert(users).values({
    email: invitation.email,
    name: userData.name,
    password: await hashPassword(userData.password),
    role: invitation.role as UserRole, // Type assertion to UserRole
    dealership_id: invitation.dealershipId !== null ? invitation.dealershipId : undefined,
    is_verified: true
  }).returning();

  // Update invitation status
  await db.update(userInvitations)
    .set({
      status: 'accepted',
      updatedAt: new Date()
    })
    .where(eq(userInvitations.id, invitation.id));

  // Log the action - simplified for now
  console.log(`User ${user.id} accepted invitation ${invitation.id} for dealership ${invitation.dealershipId}`);

  // If you want to re-enable audit logging, uncomment this code:
  /*
  await logAuditEvent({
    userId: user.id,
    dealershipId: invitation.dealershipId || undefined,
    action: 'accept_invitation',
    resourceType: 'user',
    resourceId: user.id,
    details: { invitationId: invitation.id }
  });
  */

  return user;
}

/**
 * Get pending invitations for a dealership
 */
export async function getPendingInvitations(dealershipId: number) {
  return db.select().from(userInvitations)
    .where(and(
      eq(userInvitations.dealershipId, dealershipId),
      eq(userInvitations.status, 'pending')
    ));
}

/**
 * Cancel a pending invitation
 */
export async function cancelInvitation(invitationId: number, cancelledBy: number) {
  const [invitation] = await db.update(userInvitations)
    .set({
      status: 'cancelled',
      updatedAt: new Date()
    })
    .where(eq(userInvitations.id, invitationId))
    .returning();

  if (invitation) {
    // Convert potential null to undefined for TypeScript compliance
    const dealershipId = typeof invitation.dealershipId === 'number' ? invitation.dealershipId : undefined;

    // Log the action
    await logAuditEvent({
      userId: cancelledBy,
      dealershipId,
      action: 'cancel_invitation',
      resourceType: 'user_invitation',
      resourceId: invitation.id,
      details: { email: invitation.email }
    });
  }

  return invitation;
}

/**
 * Log an audit event
 */
export async function logAuditEvent({
  userId,
  dealershipId,
  action,
  resourceType,
  resourceId,
  details,
  ipAddress
}: {
  userId?: number;
  dealershipId?: number;
  action: string;
  resourceType?: string;
  resourceId?: number;
  details?: Record<string, any>;
  ipAddress?: string;
}) {
  return db.insert(auditLogs).values({
    userId,
    dealershipId,
    action,
    resourceType,
    resourceId,
    details: details || {},
    ipAddress
  });
}

/**
 * Get audit logs for a dealership
 */
export async function getAuditLogs(dealershipId: number, limit: number = 100) {
  return db.select().from(auditLogs)
    .where(eq(auditLogs.dealershipId, dealershipId))
    .orderBy(auditLogs.createdAt)
    .limit(limit);
}

/**
 * Helper function to hash passwords securely using bcrypt
 * @param password The plaintext password to hash
 * @returns Promise resolving to the hashed password
 */
async function hashPassword(password: string): Promise<string> {
  // Generate a salt with cost factor 12 (recommended minimum for security)
  const saltRounds = 12;
  const salt = await bcrypt.genSalt(saltRounds);

  // Hash the password with the generated salt
  const hashedPassword = await bcrypt.hash(password, salt);

  return hashedPassword;
}

/**
 * Verify if a password matches its hash
 * @param plainPassword The plaintext password to check
 * @param hashedPassword The stored hash to compare against
 * @returns Promise resolving to true if password matches
 */
async function verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(plainPassword, hashedPassword);
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error verifying password', err);
    return false;
  }
}