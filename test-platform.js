/**
 * Platform Test Script
 */

import http from 'http';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Starting platform test...');

// Test 1: Server connectivity
async function testServerConnectivity() {
  console.log('\n🔍 Testing server connectivity...');
  
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        console.log('✅ Server is running and responding');
        resolve(true);
      } else {
        console.log(`❌ Server returned status code: ${res.statusCode}`);
        resolve(false);
      }
    });

    req.on('error', (error) => {
      console.log(`❌ Server connectivity failed: ${error.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      console.log('❌ Server request timed out');
      resolve(false);
    });

    req.end();
  });
}

// Test 2: Database connection
async function testDatabaseConnection() {
  console.log('\n🔍 Testing database connection...');
  
  try {
    const dbCheckScript = `
    import pg from 'pg';
    import dotenv from 'dotenv';
    
    dotenv.config();
    const { Pool } = pg;
    
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/rylie'
    });
    
    async function testConnection() {
      try {
        const client = await pool.connect();
        console.log('✅ Database connection successful');
        client.release();
        process.exit(0);
      } catch (err) {
        console.log(\`❌ Database connection failed: \${err.message}\`);
        process.exit(1);
      }
    }
    
    testConnection();
    `;
    
    const tempFile = path.join(__dirname, 'temp-db-check.mjs');
    fs.writeFileSync(tempFile, dbCheckScript);
    
    try {
      execSync(`node ${tempFile}`, { stdio: 'inherit' });
      return true;
    } catch (error) {
      return false;
    } finally {
      fs.unlinkSync(tempFile);
    }
  } catch (error) {
    console.log(`❌ Error running database test: ${error.message}`);
    return false;
  }
}

// Test 3: API endpoints
async function testApiEndpoints() {
  console.log('\n🔍 Testing API endpoints...');
  
  return new Promise((resolve) => {
    const endpoints = [
      { path: '/api/health', name: 'Health Check' },
      { path: '/api/status', name: 'API Status' }
    ];
    
    let successCount = 0;
    let completedCount = 0;
    
    endpoints.forEach(endpoint => {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path: endpoint.path,
        method: 'GET',
        timeout: 5000
      };
      
      const req = http.request(options, (res) => {
        completedCount++;
        
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`✅ ${endpoint.name} endpoint is working`);
          successCount++;
        } else {
          console.log(`❌ ${endpoint.name} endpoint returned status code: ${res.statusCode}`);
        }
        
        if (completedCount === endpoints.length) {
          resolve(successCount === endpoints.length);
        }
      });
      
      req.on('error', (error) => {
        completedCount++;
        console.log(`❌ ${endpoint.name} endpoint failed: ${error.message}`);
        
        if (completedCount === endpoints.length) {
          resolve(successCount === endpoints.length);
        }
      });
      
      req.on('timeout', () => {
        completedCount++;
        req.destroy();
        console.log(`❌ ${endpoint.name} endpoint timed out`);
        
        if (completedCount === endpoints.length) {
          resolve(successCount === endpoints.length);
        }
      });
      
      req.end();
    });
    
    if (endpoints.length === 0) {
      resolve(true);
    }
  });
}

// Run all tests
async function runTests() {
  console.log('==================================');
  console.log('🧪 PLATFORM TEST SUITE');
  console.log('==================================');
  
  const serverResult = await testServerConnectivity();
  const dbResult = await testDatabaseConnection();
  const apiResult = await testApiEndpoints();
  
  console.log('\n==================================');
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('==================================');
  console.log(`Server Connectivity: ${serverResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Database Connection: ${dbResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API Endpoints: ${apiResult ? '✅ PASS' : '❌ FAIL'}`);
  
  const overallResult = serverResult && dbResult && apiResult;
  console.log('\n==================================');
  console.log(`Overall Platform Status: ${overallResult ? '✅ OPERATIONAL' : '❌ ISSUES DETECTED'}`);
  console.log('==================================');
  
  return overallResult;
}

runTests().then(result => {
  process.exit(result ? 0 : 1);
});